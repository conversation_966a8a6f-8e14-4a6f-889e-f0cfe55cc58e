package recording;

import config.ConfigReader;
import driver.DriverManager;

import java.util.Objects;

import static utils.BaseTest.DEFAULT_PROPERTY;

public final class Recording {

    public static void startVideo() {
        if (ConfigReader.getInstance(DEFAULT_PROPERTY).getProperty("recordVideo").equalsIgnoreCase("yes")) {
            if (Objects.nonNull(DriverManager.getDriver())) {
                Video.start();
            }
        }
    }

    public static void stopVideo(String methodName) {
        if (ConfigReader.getInstance(DEFAULT_PROPERTY).getProperty("recordVideo").equalsIgnoreCase("yes")) {
            if (Objects.nonNull(DriverManager.getDriver())) {
                Video.stop(methodName);
                getScreenshot(methodName);
            }
        }
    }

    public static void getScreenshot(String methodName) {
        Screenshot.capture(methodName);
    }
}
