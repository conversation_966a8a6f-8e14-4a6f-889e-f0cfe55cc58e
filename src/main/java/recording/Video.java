package recording;

import constants.Constants;
import driver.DriverManager;
import io.appium.java_client.screenrecording.CanRecordScreen;
import logger.ConsoleLogger;
import org.slf4j.Logger;

import java.io.File;

import static constants.Constants.getCurrentDateTime;
import static utils.Util.writeToOutputStream;

public final class Video {

    private static final Logger LOGGER = ConsoleLogger.getLogger(Video.class);

    public static void start() {
        LOGGER.info("Video recording enabled, starting..");
        ((CanRecordScreen) DriverManager.getDriver()).startRecordingScreen();
    }

    public static void stop(String methodName) {
        String videoFile = ((CanRecordScreen) DriverManager.getDriver()).stopRecordingScreen();
        String pathToWriteVideoFile = Constants.getVideoPath() + File.separator + methodName + getCurrentDateTime() + ".mp4";
        writeToOutputStream(pathToWriteVideoFile, videoFile);
        LOGGER.info("Video recording saved at: {}", pathToWriteVideoFile);
    }

}
