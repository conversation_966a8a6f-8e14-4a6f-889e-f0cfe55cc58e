package recording;

import constants.Constants;
import driver.DriverManager;
import logger.ConsoleLogger;
import org.apache.commons.io.FileUtils;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.slf4j.Logger;

import java.io.File;
import java.io.IOException;

import static constants.Constants.getCurrentDateTime;

public class Screenshot {

    private static final Logger LOGGER = ConsoleLogger.getLogger(Screenshot.class);

    public static void capture(String methodName) {
        File source = ((TakesScreenshot) DriverManager.getDriver()).getScreenshotAs(OutputType.FILE);
        File destination = new File(Constants.SCREEN_RECORDING_PATH + File.separator + methodName + getCurrentDateTime() + ".png");
        try {
            FileUtils.copyFile(source, destination);
            LOGGER.info("Screenshot saved as: {}", destination);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
