package xray;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

public class Xray<PERSON>andler {
    public static void updateExecution(String testKey, String start, String finish, String comment, String status) {
        String xrayExecutionKey = System.getProperty("testExecutionKey");
        String XRAY_TOKEN = "NDg1NTc4NDA4NjkzOrULQOta2BrztjlnHrJJhU+2trV2";
        if (xrayExecutionKey == null || xrayExecutionKey.isEmpty()) {
            System.out.println("There is no provided Xray Execution ID in format -DtestExecutionKey=\"XDEMO-123\". Skipping Xray update...");
        } else {
            String jsonBody = "{" + "\"testExecutionKey\": \"" + xrayExecutionKey + "\"," + "\"tests\": [" + "{" + "\"testKey\": \"" + testKey + "\"," + "\"start\": \"" + start + "\"," + "\"finish\": \"" + finish + "\"," + "\"comment\": \"" + comment + "\"," + "\"status\": \"" + status + "\"" + "}" + "]" + "}";
            System.out.println(jsonBody);
            try {
                URL url = new URL("https://jira.eurodata.de/rest/raven/2.0/api/import/execution");

                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setRequestProperty("Authorization", "Bearer " + XRAY_TOKEN);
                connection.setDoOutput(true);
                try (OutputStream os = connection.getOutputStream()) {
                    byte[] input = jsonBody.getBytes(StandardCharsets.UTF_8);
                    os.write(input, 0, input.length);
                }
                try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    StringBuilder response = new StringBuilder();
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        response.append(responseLine.trim());
                    }
                    System.out.println("Xray result updated successfully");
                }
                connection.disconnect();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }
}
