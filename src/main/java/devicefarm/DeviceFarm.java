package devicefarm;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import driver.DriverManager;
import io.appium.java_client.AppiumDriver;
import logger.ConsoleLogger;
import org.openqa.selenium.JavascriptExecutor;

import java.util.logging.Logger;

public class DeviceFarm {
    protected static final Logger LOGGER = ConsoleLogger.getLogger();

    /**
     * Sets the test session name in Device Farm.
     *
     * @param driver AppiumDriver instance
     * @param name   Name of the session to appear in the dashboard
     */
    public static void setSessionName(AppiumDriver driver, String name) {
        try {
            if (driver instanceof JavascriptExecutor) {
                driver.executeScript("devicefarm: setSessionName", ImmutableMap.of("name", name));
                LOGGER.info("Session name set to: " + name);
            }
        } catch (Exception e) {
            LOGGER.info("Failed to set session name: " + e.getMessage());
        }
    }

    /**
     * Sets the test session status in Device Farm.
     *
     * @param status Status to set: "passed" or "failed"
     */
    public static void setSessionStatus(String status) {
        LOGGER.info("Trying to set Session status..");
        try {
            if (!status.equals("passed") && !status.equals("failed")) {
                throw new IllegalArgumentException("Invalid status: " + status);
            }

            DriverManager.getDriver().executeScript("devicefarm: setSessionStatus", ImmutableMap.of("status", status));
            LOGGER.info("Session status set to: " + status);

        } catch (Exception e) {
            LOGGER.info("Failed to set DeviceFarm session status: " + e.getMessage());
        }
    }
}
