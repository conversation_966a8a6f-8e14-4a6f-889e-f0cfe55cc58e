package commands;

import constants.Constants;
import driver.DriverManager;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.ios.IOSDriver;
import io.appium.java_client.pagefactory.AppiumFieldDecorator;

import org.openqa.selenium.interactions.PointerInput;
import org.openqa.selenium.interactions.Sequence;
import logger.ConsoleLogger;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.FluentWait;

import java.time.Duration;
import java.util.Set;
import java.util.logging.Logger;
import java.util.Collections;

import static constants.Localization.getLocalizationText;

/**
 * Provides a set of reusable Appium-based actions for both Android and iOS platforms.
 * This class simplifies interactions such as clicking, typing, swiping and context switching
 * to ensure consistency and reduce code duplication in test scripts.
 * <p>
 * Designed for use in automated UI testing across mobile platforms.
 *
 * <AUTHOR>
 * @since 1.0
 */
@SuppressWarnings("unused")
public class Actions {
    protected static final Logger LOGGER = ConsoleLogger.getLogger();
    private final AppiumDriver driver;

    public Actions() {
        this.driver = DriverManager.getDriver();
        PageFactory.initElements(new AppiumFieldDecorator(driver), this);
    }

    /**
     * Returns the current Appium driver instance.
     *
     * @return The AppiumDriver instance.
     */
    public AppiumDriver getDriver() {
        return driver;
    }

    /**
     * Navigates to the specified URL in the mobile browser context.
     *
     * @param url The URL to open.
     */
    public void navigate(String url) {
        driver.get(url);
    }

    /**
     * Clicks on the provided web element after waiting for it to be visible and clickable.
     *
     * @param element The target WebElement to click.
     */
    public void click(WebElement element) {
        LOGGER.info("Click on element: " + element.getText());
        waitForElementToBeVisible(element);
        waitForElementToBeClickable(element);
        element.click();
    }

    /**
     * Performs a JavaScript-based click on the specified web element.
     * Useful for cases where regular Selenium clicks may not work.
     *
     * @param element The element to be clicked using JavaScript.
     */
    public void clickWithJavaScriptExecutor(WebElement element) {
        waitForElementToBeVisible(element);
        waitForElementToBeClickable(element);
        JavascriptExecutor js = getDriver();
        js.executeScript("arguments[0].click();", element);
    }

    /**
     * Verifies text content of a UI element by comparing it to localized expectations for Android or iOS.
     *
     * @param expectedAndroid Expected text on Android.
     * @param expectedIOS     Expected text on iOS.
     * @param element         The element whose text should be verified.
     */
    public void verifyPlatformSpecificText(String expectedAndroid, String expectedIOS, WebElement element) {
        String expectedText = "";
        String actualText = getText(element);
        if (isAndroid()) {
            expectedText = getLocalizationText(expectedAndroid);
        } else if (isIos()) {
            expectedText = getLocalizationText(expectedIOS);
        }
        Assertions.assertEquals(expectedText, actualText, Constants.TEXT_DOES_NOT_MATCH);
    }

    /**
     * Sends the provided text to the input element.
     *
     * @param element The input WebElement.
     * @param text    The string to type.
     */
    public void type(WebElement element, String text) {
        LOGGER.info("Typing into element..");
        element.sendKeys(text);
    }

    /**
     * Clears the contents of the input element.
     *
     * @param element The element to clear.
     */
    public void clear(WebElement element) {
        element.clear();
    }

    /**
     * Returns the visible text of the element.
     *
     * @param element The element to retrieve text from.
     * @return The element's text.
     */
    public String getText(WebElement element) {
        return element.getText();
    }

    /**
     * Checks if the element is displayed.
     *
     * @param element The element to check.
     * @return True if displayed, otherwise false.
     */
    public boolean isDisplayed(WebElement element) {
        return element.isDisplayed();
    }

    /**
     * Checks if the element is not displayed or not present.
     *
     * @param element The element to check.
     * @return True if not displayed or not found, otherwise false.
     */
    public boolean isNotDisplayed(WebElement element) {
        try {
            return !element.isDisplayed();
        } catch (NoSuchElementException e) {
            return true;
        }
    }

    /**
     * Checks if the element is enabled.
     *
     * @param element The element to check.
     * @return True if enabled, otherwise false.
     */
    public boolean isEnabled(WebElement element) {
        return element.isEnabled();
    }

    /**
     * Checks if the element is disabled.
     *
     * @param element The element to check.
     * @return True if disabled, otherwise false.
     */
    public boolean isDisabled(WebElement element) {
        return !element.isEnabled();
    }

    /**
     * Determines if the current driver is an AndroidDriver.
     *
     * @return True if AndroidDriver, otherwise false.
     */
    public boolean isAndroid() {
        return driver instanceof AndroidDriver;
    }

    /**
     * Determines if the current driver is an IOSDriver.
     *
     * @return True if IOSDriver, otherwise false.
     */
    public boolean isIos() {
        return driver instanceof IOSDriver;
    }

    /**
     * Performs a swipe gesture from top to bottom of the screen.
     */
    public void swipeTopToBottom() {
        int screenWidth = driver.manage().window().getSize().getWidth();
        int screenHeight = driver.manage().window().getSize().getHeight();

        int startX = screenWidth / 2;
        int startY = (int) (screenHeight * 0.2);
        int endY = (int) (screenHeight * 0.8);

        swipe(startX, startY, startX, endY);
    }

    /**
     * Performs a swipe gesture from right to left on the given element.
     *
     * @param element The element on which to perform the swipe.
     */
    public void swipeRightToLeft(WebElement element) {
        int startX = element.getLocation().getX() + element.getSize().getWidth();
        int endX = element.getLocation().getX();
        int centerY = element.getLocation().getY() + (element.getSize().getHeight() / 2);

        swipe(startX, centerY, endX, centerY);
    }

    /**
     * Performs a swipe gesture from the center of the element to its right edge.
     *
     * @param element The element on which to perform the swipe.
     */
    public void swipeFromElementCenterToRight(WebElement element) {
        int startX = element.getLocation().getX() + (element.getSize().getWidth() / 2);
        int endX = element.getLocation().getX() + element.getSize().getWidth() - 5; // Slightly before the edge
        int centerY = element.getLocation().getY() + (element.getSize().getHeight() / 2);

        swipe(startX, centerY, endX, centerY);
    }

    /**
     * Verifies that the element's text matches the localized expected text.
     *
     * @param expectedText The key to the localized expected text.
     * @param element      The element whose text should be verified.
     */
    public void verifyLocalizedElementText(String expectedText, WebElement element) {
        String expectedLocalizedText = getLocalizationText(expectedText);
        String actualText = getText(element);
        Assertions.assertEquals(expectedLocalizedText, actualText, Constants.TEXT_DOES_NOT_MATCH);
    }

    /**
     * Verifies that the element's text matches the expected text exactly.
     *
     * @param expectedText The expected text.
     * @param element      The element whose text should be verified.
     */
    public void verifyElementText(String expectedText, WebElement element) {
        String actualText = getText(element);
        Assertions.assertEquals(expectedText, actualText, Constants.TEXT_DOES_NOT_MATCH);
    }

    /**
     * Hides the keyboard if the current driver is Android.
     * Logs a warning if the driver is not Android.
     */
    public void hideKeyboard() {
        if (isAndroid()) {
            AndroidDriver androidDriver = (AndroidDriver) driver;
            ((AndroidDriver) driver).hideKeyboard();
        } else {
            // todo: handle for ios driver
            LOGGER.warning("Driver is not an instance of AndroidDriver.");
        }
    }

    /**
     * Sets the iOS auto-accept alerts capability.
     * This method should be called selectively in tests where auto-accepting alerts is needed.
     *
     * @param autoAccept True to auto-accept alerts, false to handle them manually.
     */
    public void setIosAutoAcceptAlerts(boolean autoAccept) {
        if (isIos()) {
            IOSDriver iosDriver = (IOSDriver) driver;
            try {
                iosDriver.setSetting("autoAcceptAlerts", autoAccept);
                LOGGER.info(Constants.IOS_AUTO_ACCEPT_ALERTS_SET + autoAccept);
            } catch (Exception e) {
                LOGGER.warning(Constants.FAILED_TO_SET_IOS_AUTO_ACCEPT_ALERTS + e.getMessage());
            }
        } else {
            LOGGER.warning(Constants.IOS_AUTO_ACCEPT_ALERTS_NOT_SUPPORTED_ON_ANDROID);
        }
    }

    /**
     * Switches to the specified context view (e.g., WEBVIEW_chrome).
     * Waits up to 5 seconds, checking every second for the desired context to become available.
     *
     * @param context The context substring to match.
     */
    public void switchToView(String context) {
        boolean isSwitched = false;

        for (int i = 0; i < 5; i++) {
            Set<String> contextHandles;

            if (isAndroid()) {
                AndroidDriver androidDriver = (AndroidDriver) driver;
                contextHandles = androidDriver.getContextHandles();
                LOGGER.info("Available contexts (Android): " + contextHandles);

                for (String contextName : contextHandles) {
                    if (contextName.contains(context)) {
                        androidDriver.context(contextName);
                        LOGGER.info("Switched to context: " + contextName);
                        isSwitched = true;
                        break;
                    }
                }

            } else if (isIos()) {
                IOSDriver iosDriver = (IOSDriver) driver;
                contextHandles = iosDriver.getContextHandles();
                LOGGER.info("Available contexts (iOS): " + contextHandles);

                for (String contextName : contextHandles) {
                    if (contextName.contains(context)) {
                        iosDriver.context(contextName);
                        LOGGER.info("Switched to context: " + contextName);
                        isSwitched = true;
                        break;
                    }
                }

            } else {
                LOGGER.warning("Unsupported driver type. Unable to switch context.");
                break;
            }

            if (isSwitched) break;

            try {
                Thread.sleep(1000); // wait 1 second before next check
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Interrupted while waiting for context: " + context);
            }
        }

        if (!isSwitched) {
            LOGGER.warning("Context '" + context + "' not found after 5 seconds.");
        }

        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(10));
    }

    private void waitForElementToBeVisible(WebElement element) {
        FluentWait<AppiumDriver> fluentWait = new FluentWait<>(driver).withTimeout(Duration.ofSeconds(20)).pollingEvery(Duration.ofSeconds(1)).ignoring(NoSuchElementException.class);

        fluentWait.until(ExpectedConditions.visibilityOf(element));
    }

    private void waitForElementToBeClickable(WebElement element) {
        FluentWait<AppiumDriver> fluentWait = new FluentWait<>(driver).withTimeout(Duration.ofSeconds(20)).pollingEvery(Duration.ofSeconds(1)).ignoring(NoSuchElementException.class);

        fluentWait.until(ExpectedConditions.elementToBeClickable(element));
    }

    private void swipe(int startX, int startY, int endX, int endY) {

        PointerInput finger = new PointerInput(PointerInput.Kind.TOUCH, "finger");
        Sequence swipe = new Sequence(finger, 1);

        swipe.addAction(finger.createPointerMove(Duration.ZERO, PointerInput.Origin.viewport(), startX, startY));
        swipe.addAction(finger.createPointerDown(PointerInput.MouseButton.LEFT.asArg()));
        swipe.addAction(finger.createPointerMove(Duration.ofMillis(500), PointerInput.Origin.viewport(), endX, endY));
        swipe.addAction(finger.createPointerUp(PointerInput.MouseButton.LEFT.asArg()));

        driver.perform(Collections.singletonList(swipe));
    }
}
