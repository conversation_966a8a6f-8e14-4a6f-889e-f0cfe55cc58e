package device;

import enums.AppiumProvider;
import logger.ConsoleLogger;

import java.util.logging.Logger;

public class DeviceFetcherFactory {
    protected static final Logger LOGGER = ConsoleLogger.getLogger();

    public static DeviceFetcher getFetcher(AppiumProvider type) {
        LOGGER.info("Trying to get available devices...");
        return switch (type) {
            case MOBILE_CENTER -> new MobileCenterFetcher();
            case LOCAL -> new LocalDeviceFetcher();
            case DEVICE_FARM -> new DeviceFarmFetcher();
            case SELENIUM_GRID -> new SeleniumGridFetcher();
        };
    }
}
