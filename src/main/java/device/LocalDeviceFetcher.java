package device;

import logger.ConsoleLogger;
import org.slf4j.Logger;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;


public class LocalDeviceFetcher implements DeviceFetcher {
    private static final Logger LOGGER = ConsoleLogger.getLogger(LocalDeviceFetcher.class);

    @Override
    public List<DeviceInfo> fetchAvailableDevices(String platformName) {
        LOGGER.info("Fetching available devices for platform: " + platformName);

        if ("android".equalsIgnoreCase(platformName)) {
            List<DeviceInfo> androidDevices = getAndroidDevices();
            LOGGER.info("Found " + androidDevices.size() + " Android devices");
            return androidDevices;
        } else if ("ios".equalsIgnoreCase(platformName)) {
            List<DeviceInfo> iosDevices = getIOSDevices();
            LOGGER.info("Found " + iosDevices.size() + " iOS devices");
            return iosDevices;
        }

        LOGGER.warn("Unsupported platform: {}",  platformName);
//        LOGGER.warn("Unsupported platform: {}", platform);
        return new ArrayList<>();
    }

    private List<DeviceInfo> getAndroidDevices() {
        List<DeviceInfo> devices = new ArrayList<>();

        try {
            Process process = new ProcessBuilder("adb", "devices").start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;

            while ((line = reader.readLine()) != null) {
                if (line.endsWith("device") && !line.startsWith("List")) {
                    String udid = line.split("\t")[0];
                    String name = getDeviceModel(udid);
                    devices.add(new DeviceInfo(name, udid, "Android"));
                }
            }

            reader.close();
        } catch (Exception e) {
            throw new RuntimeException("Failed to list Android devices", e);
        }

        return devices;
    }

    private String getDeviceModel(String udid) {
        try {
            Process process = new ProcessBuilder("adb", "-s", udid, "shell", "getprop", "ro.product.model").start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String model = reader.readLine().trim();
            reader.close();
            return model.isEmpty() ? udid : model;
        } catch (Exception e) {
            return udid;
        }
    }

    private List<DeviceInfo> getIOSDevices() {
        List<DeviceInfo> devices = new ArrayList<>();
        int wdaPort = 8100; // Starting WDA port for iOS devices

        try {
            LOGGER.info("Executing: xcrun xctrace list devices");
            Process process = new ProcessBuilder("xcrun", "xctrace", "list", "devices").start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;

            while ((line = reader.readLine()) != null) {
                LOGGER.info("Processing line: {}", line);

                // Look for physical devices (contain version number in parentheses but not "Simulator")
                // Example: "iPhone XR (18.5) (00008110-000E20113A28401E)"
                if (line.contains("(") && line.contains(")") && !line.contains("Simulator") && !line.contains("==")) {
                    String[] parts = line.split("\\(");
                    if (parts.length >= 3) {
                        String name = parts[0].trim();
                        // Get the UDID from the last parentheses
                        String lastPart = parts[parts.length - 1];
                        String udid = lastPart.replace(")", "").trim();

                        // Validate UDID format (should be alphanumeric and dashes)
                        if (udid.matches("[A-Fa-f0-9-]+") && udid.length() > 10) {
                            DeviceInfo device = new DeviceInfo(name, udid, "iOS", wdaPort++);
                            devices.add(device);
                            LOGGER.info("Found iOS device: " + device);
                        }
                    }
                }

                // Also look for simulators if needed
                else if (line.contains("Simulator") && line.contains("(") && line.contains(")")) {
                    String[] parts = line.split("\\(");
                    if (parts.length >= 2) {
                        String name = parts[0].trim();
                        // Get the UDID from the last parentheses
                        String lastPart = parts[parts.length - 1];
                        String udid = lastPart.replace(")", "").trim();

                        // Validate UDID format
                        if (udid.matches("[A-Fa-f0-9-]+") && udid.length() > 10) {
                            DeviceInfo device = new DeviceInfo(name, udid, "iOS", wdaPort++);
                            devices.add(device);
                            LOGGER.info("Found iOS simulator: " + device);
                        }
                    }
                }
            }

            reader.close();
            process.waitFor();

        } catch (Exception e) {
            LOGGER.error("Failed to list iOS devices: {}", e.getMessage());
            throw new RuntimeException("Failed to list iOS devices", e);
        }

        LOGGER.info("Total iOS devices found: " + devices.size());
        return devices;
    }
}
