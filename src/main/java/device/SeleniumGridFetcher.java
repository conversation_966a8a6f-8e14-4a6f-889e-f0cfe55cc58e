package device;

import config.ConfigReader;
import org.json.JSONArray;
import org.json.JSONObject;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.ArrayList;
import java.util.List;

import static utils.BaseTest.DEFAULT_PROPERTY;

public class SeleniumGridFetcher implements DeviceFetcher {

    @Override
    public List<DeviceInfo> fetchAvailableDevices(String platformName) {
        List<DeviceInfo> devices = new ArrayList<>();
        try {
            HttpClient client = HttpClient.newHttpClient();
            String appiumUrl = ConfigReader.getInstance(DEFAULT_PROPERTY).getProperty("appiumUrl");
            URI statusUri = new URI(appiumUrl + "/status");
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(statusUri)
                    .GET()
                    .build();

            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            JSONObject json = new JSONObject(response.body());
            JSONArray nodes = json.getJSONObject("value").getJSONArray("nodes");

            for (int i = 0; i < nodes.length(); i++) {
                JSONObject node = nodes.getJSONObject(i);
                JSONArray slots = node.getJSONArray("slots");

                for (int j = 0; j < slots.length(); j++) {
                    JSONObject slot = slots.getJSONObject(j);
                    if (!slot.isNull("session")) continue;

                    JSONObject stereotype = slot.getJSONObject("stereotype");
                    String deviceName = stereotype.optString("appium:deviceName");
                    String udid = stereotype.optString("appium:udid");
                    String platform = stereotype.optString("platformName");
                    if (platform == null || platform.isEmpty()) continue;

                    platform = platform.toUpperCase();
                    if (!platform.equals("ANDROID") && !platform.equals("IOS")) {
                        System.err.println("Unsupported platform: " + platform);
                        continue;
                    }

                    if (platformName.equalsIgnoreCase(platform)) {
                        DeviceInfo device = new DeviceInfo(deviceName, udid, platform);
                        devices.add(device);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return devices;
    }
}
