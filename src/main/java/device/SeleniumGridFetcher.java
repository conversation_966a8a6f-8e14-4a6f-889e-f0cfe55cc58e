package device;

import config.ConfigReader;
import logger.ConsoleLogger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.ArrayList;
import java.util.List;

import static utils.BaseTest.DEFAULT_PROPERTY;

public class SeleniumGridFetcher implements DeviceFetcher {

    private static final Logger LOGGER = ConsoleLogger.getLogger(SeleniumGridFetcher.class);

    @Override
    public List<DeviceInfo> fetchAvailableDevices(String platformName) {
        List<DeviceInfo> devices = new ArrayList<>();
        try {
            HttpClient client = HttpClient.newHttpClient();
            String appiumUrl = ConfigReader.getInstance(DEFAULT_PROPERTY).getProperty("appiumUrl");
            URI statusUri = new URI(appiumUrl + "/status");
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(statusUri)
                    .GET()
                    .build();

            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            JSONObject json = new JSONObject(response.body());
            JSONArray nodes = json.getJSONObject("value").getJSONArray("nodes");

            for (int i = 0; i < nodes.length(); i++) {
                JSONObject node = nodes.getJSONObject(i);
                JSONArray slots = node.getJSONArray("slots");

                for (int j = 0; j < slots.length(); j++) {
                    JSONObject slot = slots.getJSONObject(j);
                    if (!slot.isNull("session")) continue;

                    JSONObject stereotype = slot.getJSONObject("stereotype");
                    String deviceName = stereotype.optString("appium:deviceName");
                    String udid = stereotype.optString("appium:udid");
                    String platform = stereotype.optString("platformName");
                    if (platform == null || platform.isEmpty()) continue;

                    platform = platform.toUpperCase();
                    if (!platform.equals("ANDROID") && !platform.equals("IOS")) {
                        LOGGER.warn("Unsupported platform: {}", platform);
                        continue;
                    }

                    if (platformName.equalsIgnoreCase(platform)) {
                        DeviceInfo device = new DeviceInfo(deviceName, udid, platform);
                        devices.add(device);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("❌ Error fetching available devices", e);
        }
        return devices;
    }
}
