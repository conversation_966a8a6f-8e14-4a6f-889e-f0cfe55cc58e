package device;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import config.ConfigReader;
import logger.ConsoleLogger;

import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import static utils.BaseTest.DEFAULT_PROPERTY;

public class MobileCenterFetcher implements DeviceFetcher {
    private static final ConfigReader config = ConfigReader.getInstance(DEFAULT_PROPERTY);
    private static final ObjectMapper mapper = new ObjectMapper();

    protected static final Logger LOGGER = ConsoleLogger.getLogger();
    private static String cachedHp4mSecret;

    @Override
    public List<DeviceInfo> fetchAvailableDevices(String platform) {
        try {
            String token = fetchAccessToken();
            return fetchDevices(token).stream().filter(device -> device.getPlatformName().equalsIgnoreCase(platform)).toList();
        } catch (Exception e) {
            throw new RuntimeException("Failed to fetch devices from Mobile Center", e);
        }
    }

    private static String fetchAccessToken() throws Exception {
        String mcUrl = config.getProperty("mc.url");

        URL url = new URL(mcUrl + "/oauth2/token");
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setDoOutput(true);

        String payload = String.format("""
                {
                    "secret": "%s",
                    "tenant": "%s",
                    "duration": 30,
                    "usedBy": "%s",
                    "client": "%s"
                }
                """, config.getProperty("mc.secret"), config.getProperty("mc.tenant"), config.getProperty("mc.usedBy"), config.getProperty("mc.client"));

        try (OutputStream os = conn.getOutputStream()) {
            os.write(payload.getBytes());
            os.flush();
        }

        if (conn.getResponseCode() != 200) {
            throw new RuntimeException("Failed to authenticate: HTTP " + conn.getResponseCode());
        }

        JsonNode node = mapper.readTree(conn.getInputStream());
        String cachedAccessToken = node.get("access_token").asText();
        cachedHp4mSecret = conn.getHeaderField("x-hp4msecret");

        return cachedAccessToken;
    }

    public static String getHp4mSecret() {
        return cachedHp4mSecret;
    }

    private static List<DeviceInfo> fetchDevices(String token) throws Exception {
        String mcUrl = config.getProperty("mc.url");
        URL url = new URL(mcUrl + "/deviceContent");

        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setRequestProperty("Authorization", "Bearer " + token);
        conn.setRequestProperty("x-hp4msecret", getHp4mSecret());

        if (conn.getResponseCode() != 200) {
            throw new RuntimeException("Failed to fetch devices: HTTP " + conn.getResponseCode());
        }

        List<DeviceInfo> availableDevices = new ArrayList<>();
        JsonNode devices = mapper.readTree(conn.getInputStream());
        LOGGER.info("Found " + devices.size() + " devices.");

        for (JsonNode device : devices) {
            boolean connected = device.path("connected").asBoolean();
            boolean isFree = "Free".equalsIgnoreCase(device.path("currentReservation").path("status").asText());
            boolean isHealthy = !device.path("healthStatus").path("error").asBoolean();

            if (connected && isFree && isHealthy) {
                String name = device.path("nickName").asText();
                String platformName = device.path("platformName").asText();
                String udid = device.path("udid").asText();

                availableDevices.add(new DeviceInfo(name, platformName, udid));
            }
        }

        return availableDevices;
    }
}