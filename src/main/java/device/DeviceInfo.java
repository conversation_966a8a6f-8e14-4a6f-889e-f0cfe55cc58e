package device;

public class DeviceInfo {
    private final String name;
    private final String platformName;
    private final String udid;
    private final int wdaPort;

    // Constructor for iOS devices with WDA port
    public DeviceInfo(String name, String udid, String platformName, int wdaPort) {
        this.name = name;
        this.udid = udid;
        this.platformName = platformName;
        this.wdaPort = wdaPort;
    }

    // Constructor for Android devices (no WDA port needed)
    public DeviceInfo(String name, String udid, String platformName) {
        this.name = name;
        this.udid = udid;
        this.platformName = platformName;
        this.wdaPort = 0; // Default for Android
    }

    public String getUdid() {
        return udid;
    }

    public String getName() {
        return name;
    }

    public String getPlatformName() {
        return platformName;
    }

    public int getWdaPort() {
        return wdaPort;
    }
}
