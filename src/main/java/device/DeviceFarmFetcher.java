package device;

import java.util.ArrayList;
import java.util.List;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

import org.json.JSONArray;
import org.json.JSONObject;

public class DeviceFarmFetcher implements DeviceFetcher {

    @Override
    public List<DeviceInfo> fetchAvailableDevices(String platformName) {
        if ("android".equalsIgnoreCase(platformName)) {
            return getAndroidDevices();
        } else if ("ios".equalsIgnoreCase(platformName)) {
            return getIOSDevices();
        }
        return new ArrayList<>();
    }

    private List<DeviceInfo> getAndroidDevices() {
        return fetchDevicesFromDeviceFarm("android");
    }

    private List<DeviceInfo> getIOSDevices() {
        return fetchDevicesFromDeviceFarm("ios");
    }

    private List<DeviceInfo> fetchDevicesFromDeviceFarm(String platform) {
        List<DeviceInfo> devices = new ArrayList<>();
        try {
            HttpClient client = HttpClient.newHttpClient();
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create("http://localhost:4723/device-farm/api/device"))
                    .GET()
                    .build();
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() == 200) {
                JSONArray jsonArray = new JSONArray(response.body());
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject deviceJson = jsonArray.getJSONObject(i);
                    if (platform.equalsIgnoreCase(deviceJson.getString("platform")) &&
                            !deviceJson.optBoolean("busy", true) &&
                            !deviceJson.optBoolean("offline", true)) {

                        devices.add(new DeviceInfo(deviceJson.getString("name"), "", deviceJson.getString("platform")));
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace(); // Optionally log this
        }
        return devices;
    }
}
