package device;

import config.ConfigReader;
import core.AppiumProviderDetector;
import enums.AppiumProvider;
import logger.ConsoleLogger;
import org.slf4j.Logger;

import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static utils.BaseTest.DEFAULT_PROPERTY;

public class DevicePool {

    private static final Logger LOGGER = ConsoleLogger.getLogger(DevicePool.class);
    private static final ConfigReader config = ConfigReader.getInstance(DEFAULT_PROPERTY);
    private static final int MAX_DEVICES = Integer.parseInt(System.getProperty("parallel", "1"));
    private static final int ALLOCATION_TIMEOUT_SECONDS = Integer.parseInt(
            config.getProperty("device.timeoutSec") != null
                    ? config.getProperty("device.timeoutSec")
                    : "10"
    );
    private static final AtomicBoolean initialized = new AtomicBoolean(false);
    private static BlockingQueue<DeviceInfo> pool;

    public static void initializeIfNeeded(String platformName) {
        if (initialized.get()) return;

        synchronized (DevicePool.class) {
            if (initialized.get()) return;

            LOGGER.info("Initializing device pool for platform: {}", platformName);
            LOGGER.info("Available processors: {}", Runtime.getRuntime().availableProcessors());
            LOGGER.info("Max test threads (parallel): {}", MAX_DEVICES);

            String appiumUrl = config.getProperty("appiumUrl");
            AppiumProvider provider = AppiumProviderDetector.detect(appiumUrl);
            DeviceFetcher fetcher = DeviceFetcherFactory.getFetcher(provider);
            List<DeviceInfo> devices = fetcher.fetchAvailableDevices(platformName);

            LOGGER.info("Found {} available devices from Device Farm: {}", devices.size(),
                    devices.stream().map(DeviceInfo::getName).toList());

            if (devices.isEmpty()) {
                throw new RuntimeException("No available devices for platform: " + platformName);
            }

            int deviceCount = devices.size();
            if (deviceCount < MAX_DEVICES) {
                LOGGER.warn("Only {} devices available, but {} test threads requested.", deviceCount, MAX_DEVICES);

            }
            pool = new ArrayBlockingQueue<>(MAX_DEVICES, true);

            String preferredDeviceName = config.getProperty("device.preferred");
            if (preferredDeviceName != null && !preferredDeviceName.isBlank()) {
                DeviceInfo preferred = devices.stream()
                        .filter(d -> preferredDeviceName.equalsIgnoreCase(d.getName()))
                        .findFirst()
                        .orElse(null);

                if (preferred != null) {
                    pool.offer(preferred);
                    LOGGER.info("Preferred device added to pool: {}", preferred.getName());
                    devices = devices.stream()
                            .filter(d -> !preferredDeviceName.equalsIgnoreCase(d.getName()))
                            .toList();
                } else {
                    LOGGER.warn("Preferred device '{}' not found among available devices.", preferredDeviceName);
                }
            }

            for (DeviceInfo device : devices) {
                pool.offer(device);
            }

            LOGGER.info("Devices added to pool: {}", pool.stream().map(DeviceInfo::getName).toList());

            initialized.set(true);
            LOGGER.info("Device pool initialized with {} devices.", pool.size());
        }
    }

    public static DeviceInfo allocateDevice(String platformName) {
        initializeIfNeeded(platformName);

        try {
            LOGGER.info("Pool before allocation: {}", pool.stream().map(DeviceInfo::getName).toList());
            LOGGER.info("[{}] Waiting for device from pool...", Thread.currentThread().getName());
            DeviceInfo device = null;
            long endTime = System.currentTimeMillis() + ALLOCATION_TIMEOUT_SECONDS * 1000L;
            while (System.currentTimeMillis() < endTime && device == null) {
                device = pool.poll(1, TimeUnit.SECONDS);
                if (device == null) {
                    LOGGER.info("[{}] No device available yet, retrying...", Thread.currentThread().getName());
                }
            }
            if (device == null) {
                throw new RuntimeException("No available devices after " +
                        ALLOCATION_TIMEOUT_SECONDS + " seconds");
            }
            LOGGER.info("[{}] Allocated device: {}", Thread.currentThread().getName(), device.getName());
            return device;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Device allocation interrupted", e);
        }
    }

    public static void releaseDevice(DeviceInfo device) {
        if (device == null) {
            LOGGER.warn("Attempted to release a null device.");
            return;
        }
        boolean released = pool.offer(device);
        if (released) {
            LOGGER.info("[{}] Released device: {}", Thread.currentThread().getName(), device.getName());
            LOGGER.info("Pool after release by [{}]: {}", Thread.currentThread().getName()
                    , pool.stream().map(DeviceInfo::getName).toList());
        } else {
            LOGGER.warn("Failed to return device to pool: {}", device.getName());
        }
    }
}
