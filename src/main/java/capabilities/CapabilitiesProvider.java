package capabilities;

import config.ConfigReader;
import core.AppiumProviderDetector;
import device.DeviceInfo;
import enums.AppiumProvider;
import org.openqa.selenium.remote.DesiredCapabilities;

public class CapabilitiesProvider {

    private static final String DEFAULT_PROPERTY = "config.properties";
    private static final ConfigReader config = ConfigReader.getInstance(DEFAULT_PROPERTY);

    public static DesiredCapabilities buildCapabilities(DeviceInfo device) {
        DesiredCapabilities caps = new DesiredCapabilities();
        String platform = device.getPlatformName().toLowerCase();

        buildBasicCapabilities(caps, device);
        buildAdditionalCapabilities(caps, platform);
        buildProviderSpecificCapabilities(caps);

        return caps;
    }

    private static void buildBasicCapabilities(DesiredCapabilities caps, DeviceInfo device) {
        caps.setCapability("platformName", capitalize(device.getPlatformName()));
        caps.setCapability("appium:deviceName", device.getName());
        caps.setCapability("appium:wdaLocalPort", device.getWdaPort());
        caps.setCapability("appium:udid", device.getUdid());
        caps.setCapability("appium:noReset", true);
    }

    private static void buildAdditionalCapabilities(DesiredCapabilities caps, String platform) {
        switch (platform) {
            case "android" -> {
                buildAndroidCapabilities(caps);
                buildAppCapabilities(caps, platform);
            }
            case "ios" -> {
                buildIOSCapabilities(caps);
                buildAppCapabilities(caps, platform);
            }
            default -> throw new IllegalArgumentException("Unsupported platform: " + platform);
        }
    }

    private static void buildAndroidCapabilities(DesiredCapabilities caps) {
        caps.setCapability("appium:automationName", "UiAutomator2");
    }
    public static void setIosAutoAcceptAlerts(DesiredCapabilities caps) {
        setIosAutoAcceptAlerts(caps, true);
    }

    public static void setIosAutoAcceptAlerts(DesiredCapabilities caps, boolean autoAccept) {
        // provera da li je platforma iOS
        Object platformCapability = caps.getCapability("platformName");
        if (platformCapability != null && "ios".equalsIgnoreCase(platformCapability.toString())) {
            // postavljamo capability na zadatu vrednost
            caps.setCapability("appium:autoAcceptAlerts", autoAccept);
        }
    }
    private static void buildIOSCapabilities(DesiredCapabilities caps) {
        caps.setCapability("appium:automationName", "XCUITest");

        // autoAcceptAlerts je po default-u ugašeno
        // Koristi setIosAutoAcceptAlerts(caps, true) u testovima kada ti treba

//        caps.setCapability("safariGarbageCollect", true);
        // Uverite se da OVO NIJE postavljeno na false, ili ga eksplicitno postavite na true
//        caps.setCapability("appium:shouldTerminateApp", true);
//        caps.setCapability("noReset", false);
        // Alternative: Use autoDismissAlerts to automatically dismiss/cancel alerts
//         caps.setCapability("appium:autoDismissAlerts", true);
    }

    private static void buildAppCapabilities(DesiredCapabilities caps, String platform) {
        if ("android".equalsIgnoreCase(platform)) {
            caps.setCapability("appium:appActivity", config.getProperty("appActivity"));
            caps.setCapability("appium:appPackage", config.getProperty("appPackage"));
        } else if ("ios".equalsIgnoreCase(platform)) {
            caps.setCapability("appium:bundleId", config.getProperty("bundleId"));
        }
    }

    private static void buildProviderSpecificCapabilities(DesiredCapabilities caps) {
        AppiumProvider provider = AppiumProviderDetector.detect(config.getProperty("appiumUrl"));
        switch (provider) {
            case MOBILE_CENTER:
                caps.setCapability("dl:oauthClientId", config.getProperty("mc.client"));
                caps.setCapability("dl:oauthClientSecret", config.getProperty("mc.secret"));
                caps.setCapability("dl:tenantId", config.getProperty("mc.tenant"));
                break;
            case DEVICE_FARM:
                caps.setCapability("df:liveVideo", true);
                caps.setCapability("df:recordVideo", true);
                caps.setCapability("df:skipReport", false);
                break;
            case LOCAL, SELENIUM_GRID:
                // No additional capabilities
                break;
            default:
                throw new IllegalArgumentException("Unsupported Appium provider: " + provider);
        }
    }

    private static String capitalize(String text) {
        if (text == null || text.isEmpty()) return text;
        return Character.toUpperCase(text.charAt(0)) + text.substring(1).toLowerCase();
    }
}
