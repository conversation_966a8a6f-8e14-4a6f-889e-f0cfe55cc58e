package utils;

import capabilities.CapabilitiesProvider;
import config.ConfigReader;
import constants.Localization;
import core.PlatformResolver;
import device.DeviceInfo;
import device.DevicePool;
import devicefarm.DeviceFarm;
import driver.DriverManager;
import io.appium.java_client.AppiumDriver;
import listener.TestListener;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInfo;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.remote.DesiredCapabilities;
import recording.Recording;

@ExtendWith(TestListener.class)
public abstract class BaseTest {

    public static final String DEFAULT_PROPERTY = "config.properties";
    protected DeviceInfo device;

    @BeforeEach
    protected void setUp(TestInfo testInfo) {
        ConfigReader config = ConfigReader.getInstance(DEFAULT_PROPERTY);
        Localization.loadLanguage(config.getProperty("language"));

        String platform = PlatformResolver.resolve();
        device = DevicePool.allocateDevice(platform);
        AppiumDriver driver = DriverManager.initializeDriver(device);
        DeviceFarm.setSessionName(driver, testInfo.getDisplayName());
        Recording.startVideo();
    }

    @AfterEach
    void tearDown(TestInfo testInfo) {
        Recording.stopVideo(testInfo.getDisplayName());
        DevicePool.releaseDevice(device);
    }

    /**
     * Setup metoda sa iOS auto-accept alerts uključenim.
     * Pozovi ovu metodu umesto standardnog setUp() u testovima gde ti treba auto-accept alerts.
     */
    protected void setUpWithIosAutoAcceptAlerts(TestInfo testInfo) {
        ConfigReader config = ConfigReader.getInstance(DEFAULT_PROPERTY);
        Localization.loadLanguage(config.getProperty("language"));

        String platform = PlatformResolver.resolve();
        device = DevicePool.allocateDevice(platform);

        // Kreiraj capabilities sa auto-accept alerts ako je iOS
        DesiredCapabilities caps = CapabilitiesProvider.buildCapabilities(device);
        if ("ios".equalsIgnoreCase(device.getPlatformName())) {
            CapabilitiesProvider.setIosAutoAcceptAlerts(caps, true);
        }

        AppiumDriver driver = DriverManager.initializeDriverWithCapabilities(device, caps);
        DeviceFarm.setSessionName(driver, testInfo.getDisplayName());
        Recording.startVideo();
    }
}
