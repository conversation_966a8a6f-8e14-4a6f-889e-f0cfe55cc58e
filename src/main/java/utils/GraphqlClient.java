package utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import config.ConfigReader;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.HashMap;
import java.util.Map;

public class GraphqlClient {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private final HttpClient httpClient;
    private final String baseBackendGraphQlUrl;
    private final String accessToken;

    public GraphqlClient(String accessToken) {
        this.httpClient = HttpClient.newHttpClient();
        this.baseBackendGraphQlUrl = ConfigReader.getInstance("api.properties").getProperty("baseGraphqlUrl");
        this.accessToken = accessToken;
    }

    /**
     * Execute a GraphQL query using the provided query string, variables and endpoint.
     *
     * @param query       The GraphQL query to execute.
     * @param variables   The variables to include with the query.
     * @param endpoint    The endpoint to send the request to.
     * @param headers     Optional headers to include in the request.
     *
     * @return The response from the server as a JSON string.
     *
     * @throws Exception If there is a problem executing the query.
     */
    public String executeQuery(String query, Map<String, Object> variables, String endpoint, Map<String, String> headers) throws Exception {
        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("query", query);
            requestBody.put("variables", variables);

            String requestBodyJson = OBJECT_MAPPER.writeValueAsString(requestBody);

            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                    .uri(URI.create(baseBackendGraphQlUrl + endpoint))
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + accessToken)
                    .POST(HttpRequest.BodyPublishers.ofString(requestBodyJson));

            if (headers != null) {
                headers.forEach(requestBuilder::header);
            }

            HttpRequest request = requestBuilder.build();
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() != 200) {
                throw new RuntimeException("Failed to execute GraphQL query: " + response.body());
            }

            return response.body();
        } catch (IOException | InterruptedException e) {
            throw new RuntimeException("Error executing GraphQL query", e);
        }
    }

    /**
     * Execute a GraphQL query using the provided query string, variables and endpoint.
     * This method is a convenience wrapper around the more general-purpose
     * executeQuery method, and is useful when no additional headers need to be specified.
     *
     * @param query       The GraphQL query to execute.
     * @param variables   The variables to include with the query.
     * @param endpoint    The endpoint to send the request to.
     *
     * @return The response from the server as a JSON string.
     *
     * @throws RuntimeException If there is a problem executing the query.
     */
    public String executeQuery(String query, Map<String, Object> variables, String endpoint) {
        try {
            return executeQuery(query, variables, endpoint, null);
        } catch (Exception e) {
            throw new RuntimeException("Error executing GraphQL query", e);
        }
    }
}
