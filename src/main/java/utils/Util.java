package utils;

import org.apache.commons.codec.binary.Base64;

import java.io.FileOutputStream;
import java.io.IOException;

public class Util {
    public static void writeToOutputStream(String filePathToWrite, String recordedVideoFile) {
        try (FileOutputStream outputStream = new FileOutputStream(filePathToWrite)) {
            outputStream.write(Base64.decodeBase64(recordedVideoFile));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
