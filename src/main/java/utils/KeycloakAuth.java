package utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import config.ConfigReader;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class KeycloakAuth {
    private static final String TOKEN_URL = ConfigReader.getInstance("api.properties").getProperty("tokenUrl");
    private static final String CLIENT_ID = ConfigReader.getInstance("api.properties").getProperty("clientId");

    /**
     * Authenticates user with Keycloak server and returns the access token.
     *
     * @param username The username to use for authentication.
     * @param password The password to use for authentication.
     *
     * @return The access token, <code>null</code> if authentication fails.
     *         It's important to understand that if the authentication fails (for example, wrong username or password),
     *         the server will return HTTP 400 and the client will throw {@link HttpResponse.BodyHandlers#ofString()} will throw
     *         {@link java.io.IOException}. If the client is not able to parse the response (for example, network error), the method
     *         will return <code>null</code>.
     */
    public static String authenticate(String username, String password) {
        try {
            HttpClient client = HttpClient.newHttpClient();
            String requestBody = String.format(
                    "grant_type=password&client_id=%s&username=%s&password=%s",
                    CLIENT_ID, java.net.URLEncoder.encode(username, StandardCharsets.UTF_8), password
            );

            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(TOKEN_URL))
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                    .build();

            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            if (response.statusCode() != 200) {
                return null;
            }

            Map<String, Object> jsonResponse = new ObjectMapper().readValue(response.body(), Map.class);
            return (String) jsonResponse.get("access_token");
        } catch (Exception e) {
            return null;
        }
    }
}
