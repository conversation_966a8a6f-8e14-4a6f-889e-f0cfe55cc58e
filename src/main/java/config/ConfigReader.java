package config;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

public class ConfigReader {

    private static final ConcurrentHashMap<String, ConfigReader> instances = new ConcurrentHashMap<>();
    private final Properties properties;

    private ConfigReader(String property) {
        properties = new Properties();
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(property)) {
            if (inputStream != null) {
                properties.load(inputStream);
            } else {
                throw new RuntimeException(property + " not found in the classpath");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static ConfigReader getInstance(String propertyFile) {
        return instances.computeIfAbsent(propertyFile, ConfigReader::new);
    }

    public String getProperty(String key) {
        return properties.getProperty(key);
    }
}
