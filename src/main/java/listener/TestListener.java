package listener;

import devicefarm.DeviceFarm;
import driver.DriverManager;
import io.appium.java_client.AppiumDriver;
import logger.ConsoleLogger;
import org.junit.jupiter.api.extension.*;
import xray.Xray;
import xray.XrayHandler;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static driver.DriverManager.getDriver;

public class TestListener implements TestWatcher, AfterAllCallback, BeforeEachCallback, AfterEachCallback {

    protected static final Logger LOGGER = ConsoleLogger.getLogger();

    /**
     * List to store the status of each test.
     */
    private final List<TestResultStatus> testResultsStatus = new ArrayList<>();
    private String startTime;
    private String endTime;
    private final String comment = "Result from automation framework";

    private String getCurrentTime() {
        ZonedDateTime currentTime = ZonedDateTime.now(ZoneId.of("UTC"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");
        return currentTime.format(formatter);
    }

    /**
     * Enum representing the possible test result statuses.
     */
    private enum TestResultStatus {
        SUCCESSFUL, ABORTED, FAILED, DISABLED
    }

    /**
     * Called when a test is disabled.
     *
     * @param context The extension context for the disabled test.
     * @param reason  An optional reason why the test is disabled.
     */
    @Override
    public void testDisabled(ExtensionContext context, Optional<String> reason) {
        testResultsStatus.add(TestResultStatus.DISABLED);

        Xray xray = context.getRequiredTestMethod().getAnnotation(Xray.class);
        LOGGER.info("Test Disabled for test: " + context.getDisplayName() + " with reason: " + reason.orElse("No reason provided!"));
        XrayHandler.updateExecution(xray.key(), startTime, endTime, comment, "BLOCKED");
        DeviceFarm.setSessionStatus("failed");
        DriverManager.quitDriver();
    }

    /**
     * Called when a test is successful.
     *
     * @param context The extension context for the successful test.
     */
    @Override
    public void testSuccessful(ExtensionContext context) {
        testResultsStatus.add(TestResultStatus.SUCCESSFUL);

        Xray xray = context.getRequiredTestMethod().getAnnotation(Xray.class);
        LOGGER.info("Test connected with Xray key: " + xray.key());
        LOGGER.info("Test Successful for test: " + context.getDisplayName());
        XrayHandler.updateExecution(xray.key(), startTime, endTime, comment, "PASS");
        DeviceFarm.setSessionStatus("passed");
        DriverManager.quitDriver();
    }

    /**
     * Called when a test is aborted.
     *
     * @param context The extension context for the aborted test.
     * @param cause   The throwable cause of the test abortion.
     */
    @Override
    public void testAborted(ExtensionContext context, Throwable cause) {
        testResultsStatus.add(TestResultStatus.ABORTED);

        Xray xray = context.getRequiredTestMethod().getAnnotation(Xray.class);
        LOGGER.warning("Test aborted for test: " + context.getDisplayName() + " with cause: " + cause.getMessage());
        XrayHandler.updateExecution(xray.key(), startTime, endTime, comment, "ABORTED");
        DeviceFarm.setSessionStatus("failed");
        DriverManager.quitDriver();
    }

    /**
     * Called when a test fails.
     *
     * @param context The extension context for the failed test.
     * @param cause   The throwable cause of the test failure.
     */
    @Override
    public void testFailed(ExtensionContext context, Throwable cause) {
        testResultsStatus.add(TestResultStatus.FAILED);
        Xray xray = context.getRequiredTestMethod().getAnnotation(Xray.class);
        LOGGER.severe("Test failed: " + context.getRequiredTestMethod());
        XrayHandler.updateExecution(xray.key(), startTime, endTime, comment, "FAIL");
        DeviceFarm.setSessionStatus("failed");
        DriverManager.quitDriver();
    }

    @Override
    public void afterEach(ExtensionContext extensionContext) {
        endTime = getCurrentTime();
        LOGGER.info("Test ended at: " + endTime);
    }

    @Override
    public void beforeEach(ExtensionContext extensionContext) {
        startTime = getCurrentTime();
        LOGGER.info("Test started at: " + startTime);
    }

    /**
     * Callback method that is invoked after all tests in a test container have been executed.
     * It provides a summary of the test results, including counts for each test result status.
     *
     * @param context The extension context for the executed tests.
     */
    @Override
    public void afterAll(ExtensionContext context) {
        Map<TestResultStatus, Long> summary = testResultsStatus.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

        LOGGER.info("Test result summary for: " + context.getDisplayName() + summary);
    }


}
