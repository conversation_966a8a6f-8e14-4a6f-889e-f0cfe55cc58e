package listener;

import devicefarm.DeviceFarm;
import driver.DriverManager;
import logger.ConsoleLogger;
import org.junit.jupiter.api.extension.*;
import org.slf4j.Logger;
import xray.Xray;
import xray.XrayHandler;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

public class TestListener implements TestWatcher, AfterAllCallback, BeforeEachCallback, AfterEachCallback {

    private static final Logger LOGGER = ConsoleLogger.getLogger(TestListener.class);

    private final List<TestResultStatus> testResultsStatus = new ArrayList<>();
    private final String comment = "Result from automation framework";
    private String startTime;
    private String endTime;

    private String getCurrentTime() {
        ZonedDateTime currentTime = ZonedDateTime.now(ZoneId.of("UTC"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");
        return currentTime.format(formatter);
    }

    @Override
    public void testDisabled(ExtensionContext context, Optional<String> reason) {
        testResultsStatus.add(TestResultStatus.DISABLED);

        Xray xray = context.getRequiredTestMethod().getAnnotation(Xray.class);
        LOGGER.info("Test Disabled for test: {} with reason: {}", context.getDisplayName(), reason.orElse("No reason provided!"));
        if (xray != null) {
            XrayHandler.updateExecution(xray.key(), startTime, endTime, comment, "BLOCKED");
        } else {
            LOGGER.warn("Xray annotation not present on test method: {}", context.getRequiredTestMethod());
        }
        DeviceFarm.setSessionStatus("failed");
        DriverManager.quitDriver();
    }

    @Override
    public void testSuccessful(ExtensionContext context) {
        testResultsStatus.add(TestResultStatus.SUCCESSFUL);

        Xray xray = context.getRequiredTestMethod().getAnnotation(Xray.class);
        if (xray != null) {
            LOGGER.info("Test connected with Xray key: {}", xray.key());
            XrayHandler.updateExecution(xray.key(), startTime, endTime, comment, "PASS");
        } else {
            LOGGER.warn("Xray annotation not present on test method: {}", context.getRequiredTestMethod());
        }
        LOGGER.info("Test Successful for test: {}", context.getDisplayName());
        DeviceFarm.setSessionStatus("passed");
        DriverManager.quitDriver();
    }

    @Override
    public void testAborted(ExtensionContext context, Throwable cause) {
        testResultsStatus.add(TestResultStatus.ABORTED);

        Xray xray = context.getRequiredTestMethod().getAnnotation(Xray.class);
        LOGGER.warn("Test aborted for test: {} with cause: {}", context.getDisplayName(), cause.getMessage());
        if (xray != null) {
            XrayHandler.updateExecution(xray.key(), startTime, endTime, comment, "ABORTED");
        } else {
            LOGGER.warn("Xray annotation not present on test method: {}", context.getRequiredTestMethod());
        }
        DeviceFarm.setSessionStatus("failed");
        DriverManager.quitDriver();
    }

    @Override
    public void testFailed(ExtensionContext context, Throwable cause) {
        testResultsStatus.add(TestResultStatus.FAILED);

        Xray xray = context.getRequiredTestMethod().getAnnotation(Xray.class);
        LOGGER.warn("Test failed: {}", context.getRequiredTestMethod());
        if (xray != null) {
            XrayHandler.updateExecution(xray.key(), startTime, endTime, comment, "FAIL");
        } else {
            LOGGER.warn("Xray annotation not present on test method: {}", context.getRequiredTestMethod());
        }
        DeviceFarm.setSessionStatus("failed");
        DriverManager.quitDriver();
    }

    @Override
    public void afterEach(ExtensionContext extensionContext) {
        endTime = getCurrentTime();
        LOGGER.info("\uD83D\uDD34 Test ended at: {}", endTime);
    }

    @Override
    public void beforeEach(ExtensionContext extensionContext) {
        startTime = getCurrentTime();
        LOGGER.info("\uD83D\uDFE2\uFE0F Test started at: {}", startTime);
    }

    @Override
    public void afterAll(ExtensionContext context) {
        Map<TestResultStatus, Long> summary = testResultsStatus.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

        LOGGER.info("Test result summary for: {}", context.getDisplayName() + summary);
    }

    private enum TestResultStatus {
        SUCCESSFUL, ABORTED, FAILED, DISABLED
    }
}
