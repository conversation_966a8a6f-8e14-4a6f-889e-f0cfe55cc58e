package driver;

import capabilities.CapabilitiesProvider;
import config.ConfigReader;
import device.DeviceInfo;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.ios.IOSDriver;
import logger.ConsoleLogger;
import org.openqa.selenium.remote.DesiredCapabilities;
import org.slf4j.Logger;

import java.net.URL;

import static utils.BaseTest.DEFAULT_PROPERTY;

public class DriverFactory {
    private static final Logger LOGGER = ConsoleLogger.getLogger(DriverFactory.class);

    public static AppiumDriver createDriver(DeviceInfo device) {
        DesiredCapabilities caps = CapabilitiesProvider.buildCapabilities(device);
        return createDriverWithCapabilities(device, caps);
    }

    public static AppiumDriver createDriverWithCapabilities(DeviceInfo device, DesiredCapabilities caps) {
        String appiumUrl = ConfigReader.getInstance(DEFAULT_PROPERTY).getProperty("appiumUrl");
        LOGGER.info("Creating driver for capabilities: {}", caps);
        try {
            return switch (device.getPlatformName().toLowerCase()) {
                case "android" -> new AndroidDriver(new URL(appiumUrl), caps);
                case "ios" -> new IOSDriver(new URL(appiumUrl), caps);
                default -> throw new IllegalArgumentException("Unsupported platform: " + device.getPlatformName());
            };
        } catch (Exception e) {
            throw new RuntimeException("Driver creation failed for: " + device.getName(), e);
        }
    }
}
