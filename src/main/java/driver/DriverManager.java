package driver;

import device.DeviceInfo;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.InteractsWithApps;
import logger.ConsoleLogger;
import org.slf4j.Logger;

import java.time.Duration;
import java.util.Objects;

public final class DriverManager {

    private static final Logger LOGGER = ConsoleLogger.getLogger(DriverManager.class);
    private static final int TIMEOUT = 30;
    private static final ThreadLocal<AppiumDriver> driverThreadLocal = new ThreadLocal<>();

    public static AppiumDriver initializeDriver(DeviceInfo device) {
        Objects.requireNonNull(device, "Device info cannot be null");

        if (driverThreadLocal.get() != null) {
            quitDriver();
        }

        AppiumDriver driver = DriverFactory.createDriver(device);

        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(TIMEOUT));
        driverThreadLocal.set(driver);
        LOGGER.info("✅ Driver initialized!");
        LOGGER.info("✅ Driver default timeout configured: " + TIMEOUT + " seconds");
        return driver;
    }

    public static AppiumDriver getDriver() {
        AppiumDriver driver = driverThreadLocal.get();
        if (driver == null) {
            throw new IllegalStateException("Driver not initialized.");
        }
        return driver;
    }

    public static void quitDriver() {
        AppiumDriver driver = driverThreadLocal.get();

        //todo - find safari package for iOS driver
        ((InteractsWithApps) driver).terminateApp("com.android.chrome");
        ((InteractsWithApps) driver).terminateApp("com.apple.mobilesafari");
//        ((InteractsWithApps) driver).terminateApp("com.compacer.capitain.qa");
//        ((InteractsWithApps) driver).removeApp("com.compacer.capitain.qa");
//        driver.manage().deleteAllCookies();
//        driver.executeScript("mobile: clearSafariCookies");
// ili za localStorage/sessionStorage
//        ((JavascriptExecutor) driver).executeScript("window.localStorage.clear();");
//        ((JavascriptExecutor) driver).executeScript("window.sessionStorage.clear();");
        if (driver != null) {
            try {
                LOGGER.info("Quiting driver....");
                driver.quit();
            } finally {
                driverThreadLocal.remove();
            }
        }
    }
}
