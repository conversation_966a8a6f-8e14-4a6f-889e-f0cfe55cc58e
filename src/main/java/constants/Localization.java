package constants;

import logger.ConsoleLogger;
import org.slf4j.Logger;

import java.util.ResourceBundle;

public class Localization {

    private static final Logger LOGGER = ConsoleLogger.getLogger(Localization.class);
    private static ResourceBundle resourceBundle;

    public static void loadLanguage(String language) {
        String lang = System.getProperty("lang", language);
        resourceBundle = ResourceBundle.getBundle(lang);
        LOGGER.info("Localization configured: {}", language);
    }

    public static String getLocalizationText(String key) {
        return getLocalizationText(key, new Object[0]);
    }

    public static String getLocalizationText(String key, Object... args) {
        String template = resourceBundle.getString(key);
        return String.format(template, args);
    }

    public static String getCurrentLanguage() {
        return resourceBundle.getLocale().toString();
    }
}
