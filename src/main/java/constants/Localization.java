package constants;

import logger.ConsoleLogger;

import java.util.ResourceBundle;
import java.util.logging.Logger;

public class Localization {
    private static ResourceBundle resourceBundle;
    private static final Logger LOGGER = ConsoleLogger.getLogger();

    public static void loadLanguage(String language) {
        String lang = System.getProperty("lang", language);
        resourceBundle = ResourceBundle.getBundle(lang);
        LOGGER.info("Localization configured: " + language);
    }

    public static String getLocalizationText(String key) {
        return resourceBundle.getString(key);
    }
}
