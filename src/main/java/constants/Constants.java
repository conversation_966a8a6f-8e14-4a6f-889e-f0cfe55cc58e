package constants;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class Constants {

    public static final String PROJECT_PATH = System.getProperty("user.dir");
    public static final String SCREEN_RECORDING_PATH = PROJECT_PATH + File.separator + "recordings";
    public static final String TEXT_DOES_NOT_MATCH = "Text does not match";

    public static String getVideoPath() {
        File screenRecordingsDir = new File(SCREEN_RECORDING_PATH);
        if (!screenRecordingsDir.exists()) {
            screenRecordingsDir.mkdir();
        }
        return SCREEN_RECORDING_PATH;
    }

    public static String getCurrentDateTime() {
        return "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd-MM-yyyy-HH:mm"));
    }
}
