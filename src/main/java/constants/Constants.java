package constants;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class Constants {

    public static final String PROJECT_PATH = System.getProperty("user.dir");
    public static final String SCREEN_RECORDING_PATH = PROJECT_PATH + File.separator + "recordings";
    public static final String TEXT_DOES_NOT_MATCH = "Text does not match";

    // iOS Auto Accept Alerts constants
    public static final String IOS_AUTO_ACCEPT_ALERTS_SET = "iOS auto-accept alerts set to: ";
    public static final String FAILED_TO_SET_IOS_AUTO_ACCEPT_ALERTS = "Failed to set iOS auto-accept alerts: ";
    public static final String IOS_AUTO_ACCEPT_ALERTS_NOT_SUPPORTED_ON_ANDROID = "iOS auto-accept alerts setting is not supported on Android platform";

    public static String getVideoPath() {
        File screenRecordingsDir = new File(SCREEN_RECORDING_PATH);
        if (!screenRecordingsDir.exists()) {
            screenRecordingsDir.mkdir();
        }
        return SCREEN_RECORDING_PATH;
    }

    public static String getCurrentDateTime() {
        return "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd-MM-yyyy-HH:mm"));
    }
}
