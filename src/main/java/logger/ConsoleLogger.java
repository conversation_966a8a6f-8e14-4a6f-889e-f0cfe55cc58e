package logger;

import java.util.logging.ConsoleHandler;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ConsoleLogger {
    private static final Logger LOGGER = Logger.getLogger(ConsoleLogger.class.getName());

    static {
        // Remove existing handlers
        Logger rootLogger = Logger.getLogger("");
        java.util.logging.Handler[] handlers = rootLogger.getHandlers();
        for (java.util.logging.Handler handler : handlers) {
            rootLogger.removeHandler(handler);
        }

        // logger configuration
        ConsoleHandler consoleHandler = new ConsoleHandler();
        consoleHandler.setLevel(Level.ALL);
        LOGGER.addHandler(consoleHandler);
        LOGGER.setLevel(Level.ALL);
    }

    /**
     * Method which returns the instance of the Logger for this class.
     * <p>
     * This method returns the logger instance associated with the class.
     * The logger is typically configured to output log messages based on the
     * underlying logging framework (e.g., SLF4J or java.util.logging).
     * <p>
     * Example usage:
     * <pre>{@code
     * Logger logger = LoggerUtil.getLogger();
     * logger.info("This is an informational message.");
     * }</pre>
     *
     * @return The logger instance for this class.
     */
    public static Logger getLogger() {
        return LOGGER;
    }
}
