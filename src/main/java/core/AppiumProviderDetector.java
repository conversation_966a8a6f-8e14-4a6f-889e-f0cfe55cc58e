package core;

import enums.AppiumProvider;
import logger.ConsoleLogger;
import org.slf4j.Logger;

public class AppiumProviderDetector {

    private static final Logger LOGGER = ConsoleLogger.getLogger(AppiumProviderDetector.class);

    public static AppiumProvider detect(String appiumUrl) {
        if (appiumUrl.contains("mobilecenter")) {
            LOGGER.info("Detected MOBILE CENTER as APPIUM provider");
            return AppiumProvider.MOBILE_CENTER;
        } else if (appiumUrl.endsWith("/wd/hub")) {
            LOGGER.info("Detected DEVICE FARM as APPIUM provider");
            return AppiumProvider.DEVICE_FARM;
        } else if (appiumUrl.endsWith(":4444")) {
            LOGGER.info("Detected SELENIUM GRID as APPIUM provider");
            return AppiumProvider.SELENIUM_GRID;
        } else {
            LOGGER.info("Detected LOCAL as APPIUM provider");
            return AppiumProvider.LOCAL;
        }
    }
}
