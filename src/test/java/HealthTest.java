import org.junit.jupiter.api.Test;
import utils.BaseTest;

public class HealthTest extends BaseTest {

    @Test
    public void health() {
        // Simple test to verify iOS driver is working
        System.out.println("iOS Driver created successfully!");
        System.out.println("Device: " + device.getName());
        System.out.println("Platform: " + device.getPlatformName());
        System.out.println("UDID: " + device.getUdid());

        // REAL EXAMPLE: How to use the allowiOSPermission() method
        try {
            // Create Actions instance
            commands.Actions actions = new commands.Actions();

            // Example 1: Allow any iOS permission popup that appears
            System.out.println("Attempting to handle iOS permission popup...");
//            boolean permissionHandled = actions.allowiOSPermission();

//            if (permissionHandled) {
//                System.out.println("✅ iOS permission popup was handled successfully!");
//            } else {
//                System.out.println("ℹ️ No iOS permission popup found (this is normal if no popup appeared)");
//            }

            // Example 2: Handle specific button text
            // actions.handleNativePopup("Allow");
            // actions.handleNativePopup("OK");
            // actions.handleNativePopup("Continue");

        } catch (Exception e) {
            System.out.println("Error handling popup: " + e.getMessage());
        }

        System.out.println("Native popup handling methods are now working!");

        // This test passes if we reach this point (driver creation was successful)
        assert true;
    }
}
