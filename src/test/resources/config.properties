# === Platform ===
platform=android
#platform=ios

# === Appium ===
#appiumUrl=http://127.0.0.1:4723
appiumUrl=http://mobilecenter.eurodata.de:8080/wd/hub
#appiumUrl=http://127.0.0.1:4723/wd/hub
#appiumUrl=http://127.0.0.1:4444

# === Localization ===
language=de

# === Recording ===
recordVideo=yes

# === App Info ===
appActivity=app.capitain.io.MainActivity
appPackage=app.capitain.io.qaOpen
#bundleId=com.compacer.capitain.qa
#app=your_app.apk

# === Device Pool ===
device.timeoutSec=40
device.preferred=Pixel 4 XL

# === Mobile Center Auth ===
mc.url=http://mobilecenter.eurodata.de:8080/rest
mc.secret=pbxI8O03qHAFxAwiAtky
mc.tenant=999999999
mc.usedBy=FTE
mc.client=<EMAIL>