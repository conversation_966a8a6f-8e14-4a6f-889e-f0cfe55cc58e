image: maven:3.8.7-eclipse-temurin-19


variables:
  MAVEN_CLI_OPTS: "-s ci_settings.xml -Dsettings.security=security-settings.xml --batch-mode"
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"

cache:
  paths:
    - .m2/repository/
    - target/

stages:
  - build
  - publish

build:
  stage: build
  tags:
    - shared2
  script:
    - mvn $MAVEN_CLI_OPTS compile

publish:
  stage: publish
  tags:
    - shared2
  script:
    - mvn $MAVEN_CLI_OPTS deploy
#  rules:
#    - if: $CI_COMMIT_BRANCH == "main"
