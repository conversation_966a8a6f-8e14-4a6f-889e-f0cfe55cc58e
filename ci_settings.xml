<settings>
<!--    <proxies>-->
<!--        <proxy>-->
<!--            <id>http-proxy</id>-->
<!--            <active>true</active>-->
<!--            <protocol>http</protocol>-->
<!--            <host>int-proxy.eurodata.de</host>-->
<!--            <port>3128</port>-->
<!--            <nonProxyHosts>localhost|lachesis.eurodata.de|**********</nonProxyHosts>-->
<!--        </proxy>-->
<!--        <proxy>-->
<!--            <id>https-proxy</id>-->
<!--            <active>true</active>-->
<!--            <protocol>https</protocol>-->
<!--            <host>int-proxy.eurodata.de</host>-->
<!--            <port>3128</port>-->
<!--            <nonProxyHosts>localhost|lachesis.eurodata.de|**********</nonProxyHosts>-->
<!--        </proxy>-->
<!--    </proxies>-->
    <mirrors>
        <mirror>
            <id>eurodata</id>
            <url>https://artifacts.eurodata.de/repository/eurodata-maven/</url>
            <mirrorOf>*</mirrorOf>
        </mirror>
    </mirrors>
    <profiles>
        <profile>
            <id>eurodata</id>
            <properties>
                <altSnapshotDeploymentRepository>eurodata::default::https://artifacts.eurodata.de/repository/eurodata-maven-snapshots/</altSnapshotDeploymentRepository>
                <altReleaseDeploymentRepository>eurodata::default::https://artifacts.eurodata.de/repository/eurodata-maven-releases/</altReleaseDeploymentRepository>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <repositories>
                <repository>
                    <id>eurodata</id>
                    <url>http://central</url><!-- bzeieht sich auf mirrorOf * -->
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>eurodata</id>
                    <url>http://central</url><!-- bzeieht sich auf mirrorOf * -->
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>
    <activeProfiles>
        <activeProfile>eurodata</activeProfile>
    </activeProfiles>
    <servers>
        <server>
            <id>eurodata</id>
            <username>sqs-appium</username><!--config your username here -->
            <password>{qWCsXr22H/wHtiAmV6To4OeCt6J1u+6CnM9iTp+Ojl0=}</password><!--put your encrypted password here -->
        </server>
    </servers>
</settings>
