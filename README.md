# Appium Test Automation Framework

A modular, scalable and maintainable test automation framework built using **Java**, **Appium**, **JUnit 5** and **Maven**. Designed for parallel execution on real devices and emulators, this framework supports both Android and iOS platforms and is intended for high-quality mobile app testing.

---

## 🚀 Features

- ✅ Cross-platform mobile testing (Android & iOS)
- ⚙️ Dynamic device pool management via `config.properties`
- 🔀 Parallel execution with `ThreadLocal` driver management
- 🔌 Mobile Center, Selenium Grid or local Appium server support
- 📦 Modular structure for easy maintainability
- 📈 Integrated reporting and logs
- 📽️ Test video recording support
- 🔐 Secure credential handling

---

## 📁 Project Structure

```
appium-framework/
├── config/                 # Property files (config.properties)
├── core/                   # Core framework components (DriverFactory, DriverManager, etc.)
├── devices/                # DevicePool and related logic
├── capabilities/           # CapabilitiesProvider implementations
├── tests/                  # Sample test classes
├── utils/                  # Utility classes
├── pom.xml                 # Maven configuration
└── README.md               # Project documentation
```

---

## 🛠️ Getting Started

### Prerequisites

- Java 11+
- Maven 3+
- Appium Server (installed or run via Appium CLI/App)
- Android SDK / Xcode (depending on target platform)
- Connected devices or emulators

### Installation

Clone the project:

```bash
git clone https://git.eurodata.de/chapter-testing/appium-framework.git
cd appium-framework
```

Install dependencies:

```bash
mvn clean install
```

---

## ⚙️ Configuration

Edit the `config/config.properties` file to set:

```properties
# === Appium ===
#appiumUrl=http://127.0.0.1:4723
appiumUrl=https://mobilecenter.eurodata.de/wd/hub

# === Localization ===
language=de

# === Recording ===
recordVideo=yes

# === App Info ===
appActivity=app.capitain.io.MainActivity
appPackage=app.capitain.io.qaOpen
#bundleId=
#app=your_app.apk

# === Device Pool ===
device.timeoutSec=40
device.preferred=Pixel 4 XL

# === Mobile Center Auth ===
mc.url=https://mobilecenter.eurodata.de/rest
mc.secret=xxx
mc.tenant=xxx
mc.usedBy=xxx
mc.client=xxx
```

> The device pool is read dynamically. No need to hardcode devices in code.

---

## 🧪 Running Tests

Run all tests:

```bash
mvn test
```

Run with parallel execution:

```bash
mvn clean test -Dparallel=2
```

Run a specific test class:

```bash
mvn -Dtest=LoginTest test
```

---

## 🧾 Reports

- Test results and reports are available under the `target/` directory.
- TestNG, Allure, or Extent reports can be integrated upon need.

---

## 🤝 Contributing

We welcome contributions! Please:

1. Fork the repository
2. Create a new branch
3. Write tests and follow the existing code style
4. Submit a pull request


---

## 📄 License

This project is licensed under the MIT License.

---


## 📌 Project Status

🚧 Actively maintained. Contributions and suggestions are welcome!

---

## 📦 Integration Guide

This framework is designed to be used as a dependency in your test projects.

### ➕ Add Dependency

In your test project's `pom.xml`, include this dependency:

```xml
<dependency>
  <groupId>com.techtailors</groupId>
  <artifactId>appium-framework</artifactId>
  <version>1.0.0</version>
</dependency>
```

> Make sure the framework is published to your internal or public Maven repository.

---

### 🛠️ Configuration Setup

Place your `config.properties` file in:

```
src/main/resources/config.properties
```

Sample configuration:

```properties
platformName=android
parallelCount=2
appiumUrl=http://localhost:4723/wd/hub
language=en
recordVideo=no
device.timeoutSec=40
device.preferred=Pixel_4_API_30
```

---

### 🧪 Running Local Appium Server

Install Appium globally using Node.js:

```bash
npm install -g appium
```

Start the server:

```bash
appium
```

For GUI version:

- Install Appium Desktop from https://github.com/appium/appium-desktop

---

### 📱 Device Requirements

#### Android

- Enable Developer Options and USB Debugging
- Accept connection on first plug-in
- Use emulator or real device
- Android SDK installed and `adb` available in PATH

#### iOS

- macOS with Xcode installed
- Real device or simulator setup
- WebDriverAgent signed with proper provisioning profile
- `carthage` and `brew` installed for dependencies

---

### 🔁 Parallel Execution

The framework supports dynamic device pooling. Define your devices in `config.properties`:

```properties
device.1.name=emulator-5554
device.1.udid=emulator-5554
device.1.platformVersion=13.0
device.1.systemPort=8200
```

Set `parallelCount` in the config to control the number of concurrent sessions.
